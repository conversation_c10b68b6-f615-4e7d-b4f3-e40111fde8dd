/**
 * مكتبة حساب التاريخ الهجري
 * تحويل دقيق من التاريخ الميلادي إلى الهجري
 */

class HijriCalculator {
    constructor() {
        // أسماء الأشهر الهجرية
        this.hijriMonths = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 
            'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان', 
            'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ];

        // أسماء الأيام
        this.dayNames = [
            'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 
            'الخميس', 'الجمعة', 'السبت'
        ];

        // التاريخ المرجعي: 1 محرم 1 هـ = 16 يوليو 622 م
        this.hijriEpoch = new Date(622, 6, 16); // 16 يوليو 622
    }

    /**
     * تحويل التاريخ الميلادي إلى هجري
     * @param {Date} gregorianDate - التاريخ الميلادي
     * @returns {Object} - التاريخ الهجري {year, month, day}
     */
    gregorianToHijri(gregorianDate) {
        // حساب عدد الأيام منذ بداية التقويم الهجري
        const daysDiff = Math.floor((gregorianDate - this.hijriEpoch) / (1000 * 60 * 60 * 24));
        
        // متوسط طول السنة الهجرية (354.367 يوم)
        const hijriYearLength = 354.367;
        
        // حساب السنة الهجرية التقريبية
        let hijriYear = Math.floor(daysDiff / hijriYearLength) + 1;
        
        // تصحيح السنة بناءً على الحسابات الدقيقة
        let remainingDays = daysDiff - this.getHijriYearStart(hijriYear);
        
        if (remainingDays < 0) {
            hijriYear--;
            remainingDays = daysDiff - this.getHijriYearStart(hijriYear);
        }

        // حساب الشهر واليوم
        const monthDay = this.getHijriMonthAndDay(hijriYear, remainingDays);
        
        return {
            year: hijriYear,
            month: monthDay.month,
            day: monthDay.day,
            monthName: this.hijriMonths[monthDay.month],
            dayName: this.dayNames[gregorianDate.getDay()]
        };
    }

    /**
     * حساب بداية السنة الهجرية بالأيام
     * @param {number} hijriYear - السنة الهجرية
     * @returns {number} - عدد الأيام منذ بداية التقويم الهجري
     */
    getHijriYearStart(hijriYear) {
        // حساب تقريبي لبداية السنة
        return Math.floor((hijriYear - 1) * 354.367);
    }

    /**
     * حساب الشهر واليوم من الأيام المتبقية في السنة
     * @param {number} hijriYear - السنة الهجرية
     * @param {number} remainingDays - الأيام المتبقية في السنة
     * @returns {Object} - {month, day}
     */
    getHijriMonthAndDay(hijriYear, remainingDays) {
        // أطوال الأشهر الهجرية (تتناوب بين 30 و 29 يوم)
        const monthLengths = this.getMonthLengths(hijriYear);
        
        let month = 0;
        let day = remainingDays + 1;
        
        for (let i = 0; i < 12; i++) {
            if (day <= monthLengths[i]) {
                month = i;
                break;
            }
            day -= monthLengths[i];
        }
        
        return { month, day };
    }

    /**
     * الحصول على أطوال الأشهر للسنة الهجرية
     * @param {number} hijriYear - السنة الهجرية
     * @returns {Array} - مصفوفة بأطوال الأشهر
     */
    getMonthLengths(hijriYear) {
        // الأشهر الفردية 30 يوم، الزوجية 29 يوم
        // مع تعديل للسنة الكبيسة
        const lengths = [];
        for (let i = 0; i < 12; i++) {
            if (i % 2 === 0) {
                lengths[i] = 30; // الأشهر الفردية
            } else {
                lengths[i] = 29; // الأشهر الزوجية
            }
        }
        
        // تعديل للسنة الكبيسة (ذو الحجة يصبح 30 يوم)
        if (this.isHijriLeapYear(hijriYear)) {
            lengths[11] = 30; // ذو الحجة
        }
        
        return lengths;
    }

    /**
     * تحديد ما إذا كانت السنة الهجرية كبيسة
     * @param {number} hijriYear - السنة الهجرية
     * @returns {boolean} - true إذا كانت كبيسة
     */
    isHijriLeapYear(hijriYear) {
        // دورة 30 سنة: السنوات الكبيسة هي 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
        const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
        const yearInCycle = hijriYear % 30;
        return leapYears.includes(yearInCycle);
    }

    /**
     * تنسيق التاريخ الهجري للعرض
     * @param {Object} hijriDate - التاريخ الهجري
     * @returns {string} - التاريخ منسق للعرض
     */
    formatHijriDate(hijriDate) {
        return `${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year} هـ`;
    }

    /**
     * الحصول على التاريخ الهجري الحالي
     * @returns {Object} - التاريخ الهجري الحالي
     */
    getCurrentHijriDate() {
        return this.gregorianToHijri(new Date());
    }

    /**
     * تحويل أكثر دقة باستخدام خوارزمية محسنة
     * @param {Date} gregorianDate - التاريخ الميلادي
     * @returns {Object} - التاريخ الهجري الدقيق
     */
    preciseGregorianToHijri(gregorianDate) {
        // استخدام خوارزمية Kuwaiti algorithm للدقة العالية
        const gYear = gregorianDate.getFullYear();
        const gMonth = gregorianDate.getMonth() + 1;
        const gDay = gregorianDate.getDate();

        let jd = this.gregorianToJulian(gYear, gMonth, gDay);
        let hijriDate = this.julianToHijri(jd);

        return {
            year: hijriDate.year,
            month: hijriDate.month - 1, // تحويل إلى فهرس المصفوفة
            day: hijriDate.day,
            monthName: this.hijriMonths[hijriDate.month - 1],
            dayName: this.dayNames[gregorianDate.getDay()]
        };
    }

    /**
     * تحويل التاريخ الميلادي إلى Julian Day
     */
    gregorianToJulian(year, month, day) {
        if (month <= 2) {
            year -= 1;
            month += 12;
        }
        
        const a = Math.floor(year / 100);
        const b = 2 - a + Math.floor(a / 4);
        
        return Math.floor(365.25 * (year + 4716)) + 
               Math.floor(30.6001 * (month + 1)) + 
               day + b - 1524.5;
    }

    /**
     * تحويل Julian Day إلى التاريخ الهجري
     */
    julianToHijri(jd) {
        jd = Math.floor(jd - 0.5) + 0.5;
        
        const l = jd - 1948440.5;
        const n = Math.floor((l - 1) / 10631);
        const l2 = l - 10631 * n + 354;
        
        const j = Math.floor((10985 - l2) / 5316) * 
                  Math.floor((50 * l2) / 17719) + 
                  Math.floor(l2 / 5670) * 
                  Math.floor((43 * l2) / 15238);
        
        const l3 = l2 - Math.floor((30 - j) / 15) * 
                   Math.floor((17719 * j) / 50) - 
                   Math.floor(j / 16) * 
                   Math.floor((15238 * j) / 43) + 29;
        
        const m = Math.floor((24 * l3) / 709);
        const d = l3 - Math.floor((709 * m) / 24);
        const y = 30 * n + j - 30;

        return { year: y, month: m, day: d };
    }
}

// تصدير الفئة للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HijriCalculator;
} else {
    window.HijriCalculator = HijriCalculator;
}